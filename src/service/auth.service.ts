import axios, { AxiosError, AxiosInstance } from "axios";
import { Auth, AuthResponse } from "@/models/auth";
import { handleApiError } from "@/lib/error-handling";

// ✅ Create a reusable Axios instance
const api: AxiosInstance = axios.create({
  baseURL: "/api", // all requests go through Next.js routes
  headers: { "Content-Type": "application/json" },
  withCredentials: true, // ensures cookies (tokens) are included
});

// ✅ Login API call
export const loginUser = async (payload: Auth): Promise<AuthResponse> => {
  try {
    const res = await api.post<AuthResponse>("/auth", payload);
    return res.data;
  } catch (error: unknown) {
    // Use helper to normalize and rethrow error
    throw handleApiError(error, "Login failed");
  }
};

// You can add more endpoints here, for example:
// export const fetchProfile = async (): Promise<User> => { ... }

export default api;
