"use client";

import Image from "next/image";
import { FC } from "react";

interface HeaderProps {
  title?: string;
}

const Header: FC<HeaderProps> = ({ title }) => {
  return (
    <header className="fixed top-0 left-0 right-0 h-14 bg-zinc-900 border-b border-zinc-800 flex items-center justify-between px-6 z-50">
      {/* Left: Logo + Title */}
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center font-bold">
          L
        </div>
        <span className="font-semibold">{title ?? "MyApp"}</span>
      </div>

      {/* Right: Profile */}
      <div className="flex items-center gap-4">
        <span className="text-sm">John <PERSON></span>
        <Image
          src="data:image/jpeg;base64,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"
          alt="profile"
          width={32} // ✅ required
          height={32} // ✅ required
          className="rounded-full border border-gray-600"
        />
      </div>
    </header>
  );
};

export default Header;
