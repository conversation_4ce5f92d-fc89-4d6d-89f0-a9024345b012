import axios, { AxiosError } from "axios";

export function handleApiError(error: unknown, defaultMessage: string): Error {
  if (axios.isAxiosError(error)) {
    const message =
      error.response?.data?.message || error.message || defaultMessage;

    // ✅ Don't create a new stack trace, just return a simple error object
    const err = new Error(message);
    err.name = "ApiError";
    return err;
  }

  if (error instanceof Error) {
    return new Error(error.message || defaultMessage);
  }

  return new Error(defaultMessage);
}
