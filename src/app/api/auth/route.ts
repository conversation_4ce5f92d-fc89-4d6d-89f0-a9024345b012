import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { Auth, AuthResponse } from "@/models/auth";

const backendUrl = process.env.API_URL;

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = (await request.json()) as Auth;
    const { email, password } = body;

    // Basic validation
    if (!email || !password) {
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    // Make API request to backend
    const res = await axios.post<AuthResponse>(
      `${backendUrl}/auth/admin/login`,
      { email, password },
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    const data = res.data;

    // Create NextResponse
    const response = NextResponse.json(
      { message: "Login successful", user: data.user },
      { status: 200 }
    );

    // 🧁 Securely store tokens as HTTP-only cookies
    response.cookies.set("accessToken", data.accessToken, {
      httpOnly: true, // ❌ cannot be accessed by JS (prevents XSS)
      secure: process.env.NODE_ENV === "production", // only HTTPS in production
      sameSite: "strict", // protects against CSRF
      path: "/", // available across the app
      expires: new Date(data.accessTokenExpiresAt),
    });

    response.cookies.set("refreshToken", data.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      expires: new Date(data.refreshTokenExpiresAt),
    });

    const roles = data.user.roles.join(",");
    response.cookies.set("roles", roles, {
      httpOnly: false, // must be readable by middleware
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
    });

    console.log("✅ Tokens stored securely in cookies.");

    return response;
  } catch (error: any) {
    console.error("Error processing login request:", error);

    if (error.response) {
      return NextResponse.json(
        {
          message: error.response.data?.message || "Invalid credentials",
        },
        { status: error.response.status || 400 }
      );
    }

    return NextResponse.json(
      { message: "An error occurred. Please try again later." },
      { status: 500 }
    );
  }
}
