"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Link,
  Image,
  ChevronLeft,
  ChevronRight,
  FileText,
  Plus,
  AlertTriangle,
  Trash,
} from "lucide-react";
import FilterSelect from "@/components/constant/filter-select";

interface PageContent {
  id: string;
  content: string;
  title: string;
}

interface RichTextEditorProps {
  pages: PageContent[];
  currentPageIndex: number;
  maxWordsPerPage?: number;
  typeFilter: string;
  onChangePageContent: (index: number, content: string) => void;
  onAddPage: () => void;
  onDeletePage: () => void;
  onChangePageIndex: (index: number) => void;
  setTypeFilter: (type: string) => void;
}

const planOption = [
  { value: "textOnly", label: "Text Only" },
  { value: "TextAndImage", label: "Text and Image" },
  { value: "ImageOnly", label: "Image Only" },
  { value: "Video", label: "Video" },
];

export function RichTextEditor({
  pages,
  currentPageIndex,
  maxWordsPerPage = 500,
  typeFilter,
  onChangePageContent,
  onAddPage,
  onDeletePage,
  onChangePageIndex,
  setTypeFilter,
}: RichTextEditorProps) {
  const currentPage = pages[currentPageIndex];
  const wordCount =
    currentPage?.content
      .replace(/<[^>]*>/g, "")
      .split(/\s+/)
      .filter((w) => w.length > 0).length || 0;

  const isPageFull = wordCount >= maxWordsPerPage;
  const isNearLimit = wordCount >= maxWordsPerPage * 0.8;

  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
  };

  const insertImage = () => {
    const url = prompt("Enter image URL:");
    if (url) document.execCommand("insertImage", false, url);
  };

  const insertLink = () => {
    const url = prompt("Enter link URL:");
    if (url) document.execCommand("createLink", false, url);
  };

  return (
    <div className="flex gap-4 mt-4">
      {/* Left Sidebar */}
      <div className="w-48 flex-shrink-0">
        <div className="space-y-0 max-h-[calc(100vh-100px)] overflow-y-auto">
          {pages.map((page, index) => (
            <div key={page.id}>
              <button
                onClick={() => onChangePageIndex(index)}
                className={`w-full text-left px-4 py-4 text-base transition-colors border-b border-white/10 ${
                  currentPageIndex === index
                    ? "text-white font-medium"
                    : "text-white/60 hover:text-white/80"
                }`}
              >
                {page.title}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 space-y-4">
        {/* Navigation */}
        <Card className="bg-white/4">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-white" />
                <div className="text-white">
                  <h3 className="font-semibold">
                    {currentPage?.title} of {pages.length}
                  </h3>
                  <p className="text-sm">Java Basics - Introduction</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white"
                  onClick={() =>
                    onChangePageIndex(Math.max(0, currentPageIndex - 1))
                  }
                  disabled={currentPageIndex === 0}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Badge variant="secondary">
                  {currentPageIndex + 1}/{pages.length}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-[#D7E1E4]"
                  onClick={() =>
                    onChangePageIndex(
                      Math.min(pages.length - 1, currentPageIndex + 1)
                    )
                  }
                  disabled={currentPageIndex === pages.length - 1}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Page Management */}
        <Card className="bg-white/4 text-white">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Page Management</CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="bg-[#016E01] border border-[#016E01]/60 hover:bg-[#016E01]/80 text-white"
                  size="sm"
                  onClick={onAddPage}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Page
                </Button>
                <Button
                  size="sm"
                  className="bg-[#D1E4EB]/62 border text-bold border-[#D1E4EB]/20 hover:bg-[#D1E4EB]/40 text-[#CD0035]"
                  onClick={onDeletePage}
                  disabled={pages.length <= 1}
                >
                  <Trash className="h-4 w-4 mr-1" />
                  Delete Page
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Warning */}
        {isNearLimit && (
          <Alert
            className={
              isPageFull
                ? "border-red-200 bg-red-50"
                : "border-yellow-200 bg-yellow-50"
            }
          >
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {isPageFull
                ? `Page is full (${wordCount}/${maxWordsPerPage} words).`
                : `Page is getting long (${wordCount}/${maxWordsPerPage} words).`}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex flex-col gap-2 w-1/2">
          <h2 className="text-lg font-semibold text-white">Enter Template</h2>
          <FilterSelect
            placeholder="Page Types"
            value={typeFilter}
            onChange={setTypeFilter}
            options={planOption}
          />
        </div>

        {/* Editor */}
        <Card className="bg-white/4 text-white">
          <CardHeader>
            <CardTitle>{currentPage?.title}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Toolbar */}
            <div className="flex flex-wrap gap-2 p-2 border rounded-lg bg-white/20">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("bold")}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("italic")}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("underline")}
              >
                <Underline className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("justifyLeft")}
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("justifyCenter")}
              >
                <AlignCenter className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("justifyRight")}
              >
                <AlignRight className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("insertUnorderedList")}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText("insertOrderedList")}
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={insertLink}>
                <Link className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={insertImage}>
                <Image className="h-4 w-4" />
              </Button>
            </div>

            <div
              className="min-h-[400px] p-4 border rounded-lg bg-white/20 focus:outline-none focus:ring-2 focus:ring-ring"
              contentEditable
              suppressContentEditableWarning={true}
              ref={(el) => {
                if (el && el.innerHTML !== currentPage?.content) {
                  el.innerHTML = currentPage?.content || "";
                }
              }}
              onInput={(e) =>
                onChangePageContent(currentPageIndex, e.currentTarget.innerHTML)
              }
              style={{ minHeight: "400px" }}
            />

            {/* Stats */}
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>
                Characters:{" "}
                {currentPage?.content.replace(/<[^>]*>/g, "").length}
              </span>
              <span>Words: {wordCount}</span>
              <span>
                Limit:{" "}
                <span
                  className={
                    wordCount >= maxWordsPerPage ? "text-red-600 font-bold" : ""
                  }
                >
                  {wordCount}/{maxWordsPerPage}
                </span>
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
