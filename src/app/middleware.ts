import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const protectedRoutes = [
  "/dashboard",
  "/subscribers",
  "/leUsers",
  "/content",
  "/finance",
  "/sponsorAds",
  "/results",
  "/auditLog",
  "/masterData",
];

const publicRoutes = ["/", "/api/auth"];

// Role-based access map
const roleAccessMap: Record<string, string[]> = {
  SUPER_ADMIN: protectedRoutes, // full access
  ADMIN: ["/dashboard", "/subscribers", "/leUsers", "/content"],
  USER: ["/dashboard", "/content"],
  CONTENT_CREATOR: ["/content"],
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const accessToken = request.cookies.get("accessToken")?.value;
  const rolesCookie = request.cookies.get("roles")?.value || "";
  const roles = rolesCookie.split(",").map((r) => r.trim());

  const isProtectedRoute = protectedRoutes.some((route) =>
    pathname.startsWith(route)
  );
  const isPublicRoute = publicRoutes.some(
    (route) => pathname === route || pathname.startsWith(route)
  );

  // --- If token exists --- //
  if (accessToken) {
    // Block public routes (login page)
    if (isPublicRoute) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    // Check role-based access for protected routes
    if (isProtectedRoute) {
      const hasAccess = roles.some((role) =>
        roleAccessMap[role]?.some((allowedPath) =>
          pathname.startsWith(allowedPath)
        )
      );

      if (!hasAccess) {
        return NextResponse.redirect(new URL("/unauthorized", request.url));
      }
    }

    return NextResponse.next();
  }

  // --- If token does not exist --- //
  if (!accessToken) {
    // Block protected routes
    if (isProtectedRoute) {
      const loginUrl = new URL("/", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Allow public routes
    if (isPublicRoute) {
      return NextResponse.next();
    }
  }

  // Default: allow all other paths
  return NextResponse.next();
}

// --- Configure middleware paths --- //
export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(png|jpg|jpeg|gif|svg)$).*)",
  ],
};
